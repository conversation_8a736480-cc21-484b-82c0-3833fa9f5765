# Relations UI Fix

## Problem
The editor is manually managing RelationComponent groups, which conflicts with the new internal management system.

## Solution
Use the new `sync_with_relation_points()` method instead of manually creating groups.

## Required Changes in editor.py

### 1. Replace manual group creation in map loading (around line 835-840):

**OLD CODE:**
```python
self.relation_component.groups = []
# Import RelationGroup
from edit_mode.ui_components import RelationGroup
self.relation_component.groups = [RelationGroup(1, self.relation_component.x, self.relation_component.y)]
self.relation_component.selected_group_index = 0
self.relation_component._reposition_groups()
```

**NEW CODE:**
```python
# Use the new sync method
self.relation_component.sync_with_relation_points({})
```

### 2. Replace manual group creation in relation points loading (around line 920-950):

**OLD CODE:**
```python
# Create groups for each ID in the relation points
# Clear existing groups first
self.relation_component.groups = []

# Sort IDs numerically
sorted_ids = []
for id_key in self.relation_points.keys():
    try:
        sorted_ids.append(int(id_key))
    except ValueError:
        pass
sorted_ids.sort()

# Create a group for each ID
for id_value in sorted_ids:
    # Create a new group at the appropriate position
    new_y = self.relation_component.y + len(self.relation_component.groups) * self.relation_component.group_spacing
    # Use the imported RelationGroup class
    from edit_mode.ui_components import RelationGroup
    self.relation_component.groups.append(
        RelationGroup(id_value, self.relation_component.x, new_y)
    )

# If no groups were created, add the default group
if not self.relation_component.groups:
    # Use the imported RelationGroup class
    from edit_mode.ui_components import RelationGroup
    self.relation_component.groups = [RelationGroup(1, self.relation_component.x, self.relation_component.y)]

# Reposition all groups
self.relation_component._reposition_groups()

# Set the selected group to the first one
self.relation_component.selected_group_index = 0
```

**NEW CODE:**
```python
# Use the new sync method
self.relation_component.sync_with_relation_points(self.relation_points)
```

### 3. Replace manual group creation in resize method (around line 1320-1350):

**OLD CODE:**
```python
# Recreate the relation groups based on the relation points
if self.relation_points:
    # Clear existing groups first
    self.relation_component.groups = []

    # Sort IDs numerically
    sorted_ids = []
    for id_key in self.relation_points.keys():
        try:
            sorted_ids.append(int(id_key))
        except ValueError:
            pass
    sorted_ids.sort()

    # Create a group for each ID
    for id_value in sorted_ids:
        # Create a new group at the appropriate position
        new_y = self.relation_component.y + len(self.relation_component.groups) * self.relation_component.group_spacing
        # Use the imported RelationGroup class
        from edit_mode.ui_components import RelationGroup
        self.relation_component.groups.append(
            RelationGroup(id_value, self.relation_component.x, new_y)
        )
```

**NEW CODE:**
```python
# Sync the relation component with the saved relation points
self.relation_component.sync_with_relation_points(saved_relation_points)
```

## Benefits of This Fix

1. **Eliminates Conflicts**: No more manual group management conflicting with internal systems
2. **Cleaner Code**: Much simpler and more maintainable
3. **Proper Positioning**: Groups are positioned correctly using the new panel system
4. **Automatic Updates**: Selection, scrolling, and bounds are handled automatically
5. **Consistent Behavior**: All group management goes through the same code path

## Implementation

The `sync_with_relation_points()` method:
- Clears existing groups
- Creates new groups based on relation points data
- Handles proper positioning and sizing
- Updates selection and scroll bounds
- Maintains backward compatibility

This fix will restore the Relations UI to proper working order while maintaining all the new modern styling and functionality.
