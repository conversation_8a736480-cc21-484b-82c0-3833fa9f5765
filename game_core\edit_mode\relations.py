"""
Relation components for the Edit Mode
"""
import pygame
from .buttons import TextButton


class RelationGroup:
    """A group of A and B buttons with an ID"""
    def __init__(self, id_value, x, y, button_size=32, spacing=10):
        self.id = id_value
        self.button_size = button_size
        self.spacing = spacing
        self.y_position = y

        # Button A (Red)
        self.button_a_rect = pygame.Rect(x, y, button_size, button_size)
        self.button_a_color = (220, 60, 60)  # Red
        self.button_a_hover_color = (255, 100, 100)  # Lighter red
        self.button_a_selected = False
        self.button_a_hovered = False

        # Button B (Blue)
        self.button_b_rect = pygame.Rect(x + button_size + spacing, y, button_size, button_size)
        self.button_b_color = (60, 60, 220)  # Blue
        self.button_b_hover_color = (100, 100, 255)  # Lighter blue
        self.button_b_selected = False
        self.button_b_hovered = False

        # ID label
        self.id_font = pygame.font.SysFont(None, 20)
        self.id_label = self.id_font.render(f"ID: {self.id}", True, (50, 50, 50))
        self.id_rect = self.id_label.get_rect(topleft=(x, y + button_size + 5))

        # Track which button is active
        self.active_button = None  # 'a', 'b', or None

    def set_position(self, x, y):
        """Set the position of the group"""
        self.y_position = y
        self.button_a_rect.topleft = (x, y)
        self.button_b_rect.topleft = (x + self.button_size + self.spacing, y)
        self.id_rect = self.id_label.get_rect(topleft=(x, y + self.button_size + 5))

    def update(self, mouse_pos):
        """Update button states based on mouse position"""
        self.button_a_hovered = self.button_a_rect.collidepoint(mouse_pos)
        self.button_b_hovered = self.button_b_rect.collidepoint(mouse_pos)

    def handle_event(self, event, mouse_pos):
        """Handle mouse events for the group"""
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            # Check if button A was clicked
            if self.button_a_hovered:
                # Toggle selection state
                was_selected = self.button_a_selected
                self.button_a_selected = not was_selected

                if self.button_a_selected:
                    self.active_button = 'a'
                    # Deselect button B if A is selected
                    self.button_b_selected = False
                else:
                    # Only clear active_button if we're deselecting
                    if was_selected:
                        self.active_button = None
                return True

            # Check if button B was clicked
            elif self.button_b_hovered:
                # Toggle selection state
                was_selected = self.button_b_selected
                self.button_b_selected = not was_selected

                if self.button_b_selected:
                    self.active_button = 'b'
                    # Deselect button A if B is selected
                    self.button_a_selected = False
                else:
                    # Only clear active_button if we're deselecting
                    if was_selected:
                        self.active_button = None
                return True

        return False

    def draw(self, surface):
        """Draw the group on the surface"""
        # Draw button A
        a_color = self.button_a_hover_color if self.button_a_hovered else self.button_a_color
        pygame.draw.rect(surface, a_color, self.button_a_rect)

        # Draw button A border (thicker if selected)
        border_width_a = 3 if self.button_a_selected else 1
        pygame.draw.rect(surface, (0, 0, 0), self.button_a_rect, border_width_a)

        # Draw button B
        b_color = self.button_b_hover_color if self.button_b_hovered else self.button_b_color
        pygame.draw.rect(surface, b_color, self.button_b_rect)

        # Draw button B border (thicker if selected)
        border_width_b = 3 if self.button_b_selected else 1
        pygame.draw.rect(surface, (0, 0, 0), self.button_b_rect, border_width_b)

        # Draw labels on the buttons
        font = pygame.font.SysFont(None, 20)

        # Label for button A
        label_a = font.render("A", True, (255, 255, 255))
        label_a_rect = label_a.get_rect(center=self.button_a_rect.center)
        surface.blit(label_a, label_a_rect)

        # Label for button B
        label_b = font.render("B", True, (255, 255, 255))
        label_b_rect = label_b.get_rect(center=self.button_b_rect.center)
        surface.blit(label_b, label_b_rect)

        # Draw ID label
        surface.blit(self.id_label, self.id_rect)

    @property
    def is_active(self):
        """Check if any button is selected"""
        return self.button_a_selected or self.button_b_selected

    @property
    def height(self):
        """Get the total height of the group"""
        return self.button_size + 25  # Button height + spacing for ID label


class RelationComponent:
    """A component with multiple groups of A and B buttons for map relations"""
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.button_size = 32
        self.spacing = 10
        self.group_spacing = 60  # Vertical spacing between groups

        # Create the first group
        self.groups = [RelationGroup(1, x, y)]

        # Add and Delete buttons
        self.add_button = TextButton(x, y + 200, 80, 30, "Add")
        self.delete_button = TextButton(x + 90, y + 200, 80, 30, "Delete")

        # Currently selected group index
        self.selected_group_index = 0

    def update(self, mouse_pos):
        """Update all groups and buttons"""
        # Update all groups
        for group in self.groups:
            group.update(mouse_pos)

        # Update add and delete buttons
        self.add_button.update(mouse_pos)
        self.delete_button.update(mouse_pos)

    def handle_event(self, event, mouse_pos):
        """Handle events for all groups and buttons"""
        # Check if any group was clicked
        for i, group in enumerate(self.groups):
            if group.handle_event(event, mouse_pos):
                # Deselect all other groups
                for j, other_group in enumerate(self.groups):
                    if j != i:
                        other_group.button_a_selected = False
                        other_group.button_b_selected = False
                        other_group.active_button = None

                # Set the selected group
                self.selected_group_index = i
                return True

        # Check if add button was clicked
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            if self.add_button.is_clicked(event):
                # Add a new group with the next ID
                new_id = len(self.groups) + 1
                new_y = self.y + len(self.groups) * self.group_spacing
                self.groups.append(RelationGroup(new_id, self.x, new_y))

                # Reposition all groups
                self._reposition_groups()
                return True

            # Check if delete button was clicked
            elif self.delete_button.is_clicked(event) and len(self.groups) > 1:
                # Remove the last group
                self.groups.pop()

                # If the selected group was removed, select the last group
                if self.selected_group_index >= len(self.groups):
                    self.selected_group_index = len(self.groups) - 1

                # Reposition all groups
                self._reposition_groups()
                return True

        return False

    def _reposition_groups(self):
        """Reposition all groups based on their order"""
        for i, group in enumerate(self.groups):
            group.set_position(self.x, self.y + i * self.group_spacing)

    def set_button_positions(self, x, y, bottom_y=None):
        """Set the positions of all buttons"""
        self.x = x
        self.y = y

        # Reposition all groups
        self._reposition_groups()

        # Set add and delete button positions at the bottom if provided
        if bottom_y:
            self.add_button.topleft = (x, bottom_y - 40)
            self.delete_button.topleft = (x + 90, bottom_y - 40)

    def draw(self, surface):
        """Draw all groups and buttons"""
        # Draw all groups
        for group in self.groups:
            group.draw(surface)

        # Draw add and delete buttons
        self.add_button.draw(surface)
        self.delete_button.draw(surface)

    @property
    def is_active(self):
        """Check if any button in any group is selected"""
        return any(group.is_active for group in self.groups)

    @property
    def active_button(self):
        """Get the active button type ('a', 'b', or None)"""
        if self.selected_group_index < len(self.groups):
            return self.groups[self.selected_group_index].active_button
        return None

    @property
    def current_id(self):
        """Get the ID of the currently selected group"""
        if self.selected_group_index < len(self.groups):
            return self.groups[self.selected_group_index].id
        return 1

    def set_id(self, id_value):
        """Set the ID of the currently selected group (for backward compatibility)"""
        # This method is kept for backward compatibility
        # Find or create a group with the given ID
        for i, group in enumerate(self.groups):
            if group.id == id_value:
                self.selected_group_index = i
                return

        # If not found, create groups up to this ID
        while len(self.groups) < id_value:
            new_id = len(self.groups) + 1
            new_y = self.y + len(self.groups) * self.group_spacing
            self.groups.append(RelationGroup(new_id, self.x, new_y))

        # Reposition all groups
        self._reposition_groups()

        # Select the group with the given ID
        self.selected_group_index = id_value - 1
